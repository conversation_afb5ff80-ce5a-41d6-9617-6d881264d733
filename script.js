// 全局变量
let chart;
let currentSpeed = '2900';
let currentImpeller = '250';

// 初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeChart();
    setupEventListeners();
});

// 设置事件监听器
function setupEventListeners() {
    const speedSelect = document.getElementById('speedSelect');
    const impellerSelect = document.getElementById('impellerSelect');
    
    speedSelect.addEventListener('change', function() {
        currentSpeed = this.value;
        updateChart();
    });
    
    impellerSelect.addEventListener('change', function() {
        currentImpeller = this.value;
        updateChart();
    });
}

// 初始化图表
function initializeChart() {
    const ctx = document.getElementById('pumpChart').getContext('2d');
    
    chart = new Chart(ctx, {
        type: 'line',
        data: getChartData(),
        options: getChartOptions()
    });
}

// 获取图表数据
function getChartData() {
    const data = getPumpData(currentSpeed, currentImpeller);
    
    return {
        datasets: [
            {
                label: '扬程 H (m)',
                data: data.flow.map((flow, index) => ({
                    x: flow,
                    y: data.head[index]
                })),
                borderColor: '#dc2626',
                backgroundColor: 'rgba(220, 38, 38, 0.1)',
                borderWidth: 3,
                fill: false,
                tension: 0.4,
                yAxisID: 'y'
            },
            {
                label: '效率 η (%)',
                data: data.flow.map((flow, index) => ({
                    x: flow,
                    y: data.efficiency[index]
                })),
                borderColor: '#059669',
                backgroundColor: 'rgba(5, 150, 105, 0.1)',
                borderWidth: 3,
                fill: false,
                tension: 0.4,
                yAxisID: 'y1'
            },
            {
                label: '功率 P (kW)',
                data: data.flow.map((flow, index) => ({
                    x: flow,
                    y: data.power[index]
                })),
                borderColor: '#2563eb',
                backgroundColor: 'rgba(37, 99, 235, 0.1)',
                borderWidth: 3,
                fill: false,
                tension: 0.4,
                yAxisID: 'y2'
            },
            {
                label: 'NPSH (m)',
                data: data.flow.map((flow, index) => ({
                    x: flow,
                    y: data.npsh[index]
                })),
                borderColor: '#7c3aed',
                backgroundColor: 'rgba(124, 58, 237, 0.1)',
                borderWidth: 3,
                fill: false,
                tension: 0.4,
                yAxisID: 'y3'
            }
        ]
    };
}

// 获取图表配置选项
function getChartOptions() {
    return {
        responsive: true,
        maintainAspectRatio: false,
        interaction: {
            mode: 'index',
            intersect: false,
        },
        plugins: {
            title: {
                display: true,
                text: `NBG 300-250-500/485 性能曲线 (${currentSpeed} rpm, Ø${currentImpeller}mm)`,
                font: {
                    size: 16,
                    weight: 'bold'
                },
                color: '#1e3a8a'
            },
            legend: {
                display: false
            },
            tooltip: {
                callbacks: {
                    title: function(context) {
                        return `流量: ${context[0].parsed.x} m³/h`;
                    },
                    label: function(context) {
                        const label = context.dataset.label;
                        const value = Math.round(context.parsed.y * 100) / 100;
                        return `${label}: ${value}`;
                    }
                }
            }
        },
        scales: {
            x: {
                type: 'linear',
                display: true,
                title: {
                    display: true,
                    text: '流量 Q (m³/h)',
                    font: {
                        size: 14,
                        weight: 'bold'
                    },
                    color: '#374151'
                },
                grid: {
                    color: '#e5e7eb',
                    lineWidth: 1
                },
                ticks: {
                    color: '#6b7280'
                }
            },
            y: {
                type: 'linear',
                display: true,
                position: 'left',
                title: {
                    display: true,
                    text: '扬程 H (m)',
                    font: {
                        size: 14,
                        weight: 'bold'
                    },
                    color: '#dc2626'
                },
                grid: {
                    color: '#fee2e2',
                    lineWidth: 1
                },
                ticks: {
                    color: '#dc2626'
                }
            },
            y1: {
                type: 'linear',
                display: true,
                position: 'right',
                title: {
                    display: true,
                    text: '效率 η (%)',
                    font: {
                        size: 14,
                        weight: 'bold'
                    },
                    color: '#059669'
                },
                grid: {
                    drawOnChartArea: false,
                },
                ticks: {
                    color: '#059669'
                },
                max: 100
            },
            y2: {
                type: 'linear',
                display: false,
                position: 'right'
            },
            y3: {
                type: 'linear',
                display: false,
                position: 'right'
            }
        },
        onClick: function(event, elements) {
            if (elements.length > 0) {
                const datasetIndex = elements[0].datasetIndex;
                const index = elements[0].index;
                const data = getPumpData(currentSpeed, currentImpeller);
                const flow = data.flow[index];
                
                showOperatingPoint(flow);
            }
        }
    };
}

// 更新图表
function updateChart() {
    chart.data = getChartData();
    chart.options.plugins.title.text = `NBG 300-250-500/485 性能曲线 (${currentSpeed} rpm, Ø${currentImpeller}mm)`;
    chart.update();
}

// 显示工作点信息
function showOperatingPoint(flow) {
    const point = getOperatingPoint(flow, currentSpeed, currentImpeller);
    const info = document.getElementById('operatingPointInfo');
    
    info.innerHTML = `
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-top: 10px;">
            <div><strong>流量:</strong> ${Math.round(point.flow * 10) / 10} m³/h</div>
            <div><strong>扬程:</strong> ${Math.round(point.head * 10) / 10} m</div>
            <div><strong>效率:</strong> ${Math.round(point.efficiency * 10) / 10} %</div>
            <div><strong>功率:</strong> ${Math.round(point.power * 10) / 10} kW</div>
            <div><strong>NPSH:</strong> ${Math.round(point.npsh * 10) / 10} m</div>
            <div><strong>转速:</strong> ${currentSpeed} rpm</div>
        </div>
    `;
}
