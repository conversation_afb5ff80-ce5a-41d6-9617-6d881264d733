// 全局变量
let chart;
let currentSpeed = '2900';
let currentImpeller = '250';

// 初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeTabs();
    initializeChart();
    setupEventListeners();
});

// 初始化标签页
function initializeTabs() {
    const tabBtns = document.querySelectorAll('.tab-btn');
    const tabPanels = document.querySelectorAll('.tab-panel');

    tabBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const targetTab = this.getAttribute('data-tab');

            // 移除所有活动状态
            tabBtns.forEach(b => b.classList.remove('active'));
            tabPanels.forEach(p => p.classList.remove('active'));

            // 添加活动状态
            this.classList.add('active');
            document.getElementById(targetTab + '-panel').classList.add('active');
        });
    });
}

// 设置事件监听器
function setupEventListeners() {
    const speedSelect = document.getElementById('speedSelect');
    const impellerSelect = document.getElementById('impellerSelect');

    if (speedSelect) {
        speedSelect.addEventListener('change', function() {
            currentSpeed = this.value;
            updateChart();
        });
    }

    if (impellerSelect) {
        impellerSelect.addEventListener('change', function() {
            currentImpeller = this.value;
            updateChart();
        });
    }
}

// 初始化图表
function initializeChart() {
    const ctx = document.getElementById('pumpChart').getContext('2d');
    
    chart = new Chart(ctx, {
        type: 'line',
        data: getChartData(),
        options: getChartOptions()
    });
}

// 获取图表数据
function getChartData() {
    const data = getPumpData(currentSpeed, currentImpeller);
    
    return {
        datasets: [
            {
                label: '扬程 H (m)',
                data: data.flow.map((flow, index) => ({
                    x: flow,
                    y: data.head[index]
                })),
                borderColor: '#ff0000',
                backgroundColor: 'rgba(255, 0, 0, 0.1)',
                borderWidth: 2,
                fill: false,
                tension: 0.3,
                yAxisID: 'y',
                pointRadius: 0,
                pointHoverRadius: 4
            },
            {
                label: '效率 η (%)',
                data: data.flow.map((flow, index) => ({
                    x: flow,
                    y: data.efficiency[index]
                })),
                borderColor: '#00aa00',
                backgroundColor: 'rgba(0, 170, 0, 0.1)',
                borderWidth: 2,
                fill: false,
                tension: 0.3,
                yAxisID: 'y1',
                pointRadius: 0,
                pointHoverRadius: 4
            },
            {
                label: '功率 P (kW)',
                data: data.flow.map((flow, index) => ({
                    x: flow,
                    y: data.power[index]
                })),
                borderColor: '#0066cc',
                backgroundColor: 'rgba(0, 102, 204, 0.1)',
                borderWidth: 2,
                fill: false,
                tension: 0.3,
                yAxisID: 'y2',
                pointRadius: 0,
                pointHoverRadius: 4
            },
            {
                label: 'NPSH (m)',
                data: data.flow.map((flow, index) => ({
                    x: flow,
                    y: data.npsh[index]
                })),
                borderColor: '#ff6600',
                backgroundColor: 'rgba(255, 102, 0, 0.1)',
                borderWidth: 2,
                fill: false,
                tension: 0.3,
                yAxisID: 'y3',
                pointRadius: 0,
                pointHoverRadius: 4
            }
        ]
    };
}

// 获取图表配置选项
function getChartOptions() {
    return {
        responsive: true,
        maintainAspectRatio: false,
        interaction: {
            mode: 'index',
            intersect: false,
        },
        plugins: {
            title: {
                display: true,
                text: `性能曲线 - ${currentSpeed} rpm, Ø${currentImpeller}mm`,
                font: {
                    size: 14,
                    weight: 'bold'
                },
                color: '#003d6b',
                padding: 10
            },
            legend: {
                display: false
            },
            tooltip: {
                backgroundColor: 'rgba(0, 61, 107, 0.9)',
                titleColor: 'white',
                bodyColor: 'white',
                borderColor: '#003d6b',
                borderWidth: 1,
                callbacks: {
                    title: function(context) {
                        return `流量: ${context[0].parsed.x} m³/h`;
                    },
                    label: function(context) {
                        const label = context.dataset.label;
                        const value = Math.round(context.parsed.y * 100) / 100;
                        return `${label}: ${value}`;
                    }
                }
            }
        },
        scales: {
            x: {
                type: 'linear',
                display: true,
                title: {
                    display: true,
                    text: '流量 Q (m³/h)',
                    font: {
                        size: 12,
                        weight: 'normal'
                    },
                    color: '#333'
                },
                grid: {
                    color: '#e0e0e0',
                    lineWidth: 1
                },
                ticks: {
                    color: '#666',
                    font: {
                        size: 11
                    }
                }
            },
            y: {
                type: 'linear',
                display: true,
                position: 'left',
                title: {
                    display: true,
                    text: '扬程 H (m)',
                    font: {
                        size: 12,
                        weight: 'normal'
                    },
                    color: '#ff0000'
                },
                grid: {
                    color: '#f0f0f0',
                    lineWidth: 1
                },
                ticks: {
                    color: '#ff0000',
                    font: {
                        size: 11
                    }
                }
            },
            y1: {
                type: 'linear',
                display: true,
                position: 'right',
                title: {
                    display: true,
                    text: '效率 η (%)',
                    font: {
                        size: 12,
                        weight: 'normal'
                    },
                    color: '#00aa00'
                },
                grid: {
                    drawOnChartArea: false,
                },
                ticks: {
                    color: '#00aa00',
                    font: {
                        size: 11
                    }
                },
                max: 100
            },
            y2: {
                type: 'linear',
                display: false,
                position: 'right'
            },
            y3: {
                type: 'linear',
                display: false,
                position: 'right'
            }
        },
        onClick: function(event, elements) {
            if (elements.length > 0) {
                const datasetIndex = elements[0].datasetIndex;
                const index = elements[0].index;
                const data = getPumpData(currentSpeed, currentImpeller);
                const flow = data.flow[index];
                
                showOperatingPoint(flow);
            }
        }
    };
}

// 更新图表
function updateChart() {
    chart.data = getChartData();
    chart.options.plugins.title.text = `NBG 300-250-500/485 性能曲线 (${currentSpeed} rpm, Ø${currentImpeller}mm)`;
    chart.update();
}

// 显示工作点信息
function showOperatingPoint(flow) {
    const point = getOperatingPoint(flow, currentSpeed, currentImpeller);
    const info = document.getElementById('operatingPointInfo');
    
    info.innerHTML = `
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-top: 10px;">
            <div><strong>流量:</strong> ${Math.round(point.flow * 10) / 10} m³/h</div>
            <div><strong>扬程:</strong> ${Math.round(point.head * 10) / 10} m</div>
            <div><strong>效率:</strong> ${Math.round(point.efficiency * 10) / 10} %</div>
            <div><strong>功率:</strong> ${Math.round(point.power * 10) / 10} kW</div>
            <div><strong>NPSH:</strong> ${Math.round(point.npsh * 10) / 10} m</div>
            <div><strong>转速:</strong> ${currentSpeed} rpm</div>
        </div>
    `;
}
