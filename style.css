/* 重置样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #ffffff;
    color: #333333;
    line-height: 1.6;
}

/* 顶部导航栏 */
.top-header {
    background: #ffffff;
    border-bottom: 1px solid #e5e7eb;
    position: sticky;
    top: 0;
    z-index: 1000;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.header-container {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 20px;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 15px;
}

.menu-toggle {
    background: none;
    border: none;
    font-size: 18px;
    cursor: pointer;
    color: #374151;
}

.logo {
    height: 40px;
}

.header-right {
    display: flex;
    align-items: center;
}

.search-btn {
    background: none;
    border: none;
    font-size: 18px;
    cursor: pointer;
    color: #374151;
    padding: 8px;
}

/* 面包屑导航 */
.breadcrumb {
    background: #f8fafc;
    border-bottom: 1px solid #e5e7eb;
}

.breadcrumb-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 12px 20px;
    font-size: 14px;
}

.breadcrumb a {
    color: #dc2626;
    text-decoration: none;
}

.breadcrumb a:hover {
    text-decoration: underline;
}

.breadcrumb span {
    color: #6b7280;
    margin: 0 8px;
}

.breadcrumb .current {
    color: #374151;
    font-weight: 500;
}

/* 主要内容区域 */
.main-content {
    background: #ffffff;
}

.content-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 30px 20px;
}

/* 产品标题区域 */
.product-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid #e5e7eb;
}

.product-title h1 {
    color: #1f2937;
    font-size: 2.5em;
    font-weight: 600;
    margin-bottom: 8px;
}

.product-code {
    color: #6b7280;
    font-size: 1.1em;
    font-weight: 500;
}

.product-image {
    flex-shrink: 0;
}

.pump-image-placeholder {
    background: #f9fafb;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 20px;
}

/* 标签页导航 */
.tab-navigation {
    display: flex;
    border-bottom: 2px solid #e5e7eb;
    margin-bottom: 30px;
}

.tab-btn {
    background: none;
    border: none;
    padding: 15px 25px;
    font-size: 16px;
    font-weight: 500;
    color: #6b7280;
    cursor: pointer;
    border-bottom: 3px solid transparent;
    transition: all 0.3s ease;
}

.tab-btn:hover {
    color: #dc2626;
    background: #fef2f2;
}

.tab-btn.active {
    color: #dc2626;
    border-bottom-color: #dc2626;
    background: #fef2f2;
}

/* 标签页内容 */
.tab-content {
    min-height: 600px;
}

.tab-panel {
    display: none;
}

.tab-panel.active {
    display: block;
}

/* 概述内容 */
.overview-content h2 {
    color: #1f2937;
    font-size: 1.8em;
    margin-bottom: 20px;
}

.overview-content p {
    color: #4b5563;
    font-size: 1.1em;
    margin-bottom: 20px;
}

.key-features h3 {
    color: #1f2937;
    font-size: 1.4em;
    margin-bottom: 15px;
}

.key-features ul {
    list-style: none;
    padding-left: 0;
}

.key-features li {
    color: #4b5563;
    padding: 8px 0;
    padding-left: 25px;
    position: relative;
}

.key-features li:before {
    content: "✓";
    color: #dc2626;
    font-weight: bold;
    position: absolute;
    left: 0;
}

/* 曲线内容 */
.curves-content {
    background: #ffffff;
}

.controls-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    padding: 20px;
    background: #f8fafc;
    border-radius: 8px;
    border: 1px solid #e5e7eb;
}

.controls-header h2 {
    color: #1f2937;
    font-size: 1.8em;
    font-weight: 600;
}

.chart-controls {
    display: flex;
    gap: 25px;
}

.control-group {
    display: flex;
    align-items: center;
    gap: 10px;
}

.control-group label {
    font-weight: 500;
    color: #374151;
    font-size: 14px;
}

.control-group select {
    padding: 8px 15px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    background: white;
    font-size: 14px;
    color: #374151;
    cursor: pointer;
}

.control-group select:focus {
    outline: none;
    border-color: #dc2626;
    box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
}

/* 图表区域 */
.chart-wrapper {
    position: relative;
    height: 500px;
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 25px;
    margin-bottom: 25px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.chart-legend {
    display: flex;
    justify-content: center;
    gap: 35px;
    flex-wrap: wrap;
    padding: 20px;
    background: #f8fafc;
    border-radius: 8px;
    border: 1px solid #e5e7eb;
    margin-bottom: 25px;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 14px;
    font-weight: 500;
    color: #374151;
}

.legend-color {
    width: 24px;
    height: 4px;
    border-radius: 2px;
}

.head-curve {
    background: #dc2626;
}

.efficiency-curve {
    background: #059669;
}

.power-curve {
    background: #2563eb;
}

.npsh-curve {
    background: #7c3aed;
}

/* 工作点信息 */
.operating-point {
    background: #f0f9ff;
    padding: 25px;
    border-radius: 8px;
    border: 1px solid #bae6fd;
    margin-top: 25px;
}

.operating-point h3 {
    color: #0c4a6e;
    font-size: 1.3em;
    margin-bottom: 15px;
    font-weight: 600;
}

#operatingPointInfo {
    color: #0369a1;
    font-size: 15px;
}

/* 技术数据内容 */
.technical-content h2 {
    color: #1f2937;
    font-size: 1.8em;
    margin-bottom: 25px;
    font-weight: 600;
}

.technical-tables {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 30px;
}

.table-section h3 {
    color: #1f2937;
    font-size: 1.3em;
    margin-bottom: 15px;
    font-weight: 600;
    border-bottom: 2px solid #dc2626;
    padding-bottom: 8px;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.data-table tr {
    border-bottom: 1px solid #e5e7eb;
}

.data-table tr:last-child {
    border-bottom: none;
}

.data-table td {
    padding: 12px 15px;
    font-size: 14px;
}

.data-table td:first-child {
    background: #f8fafc;
    font-weight: 500;
    color: #374151;
    width: 50%;
}

.data-table td:last-child {
    color: #1f2937;
    font-weight: 600;
}

/* 尺寸内容 */
.dimensions-content h2 {
    color: #1f2937;
    font-size: 1.8em;
    margin-bottom: 25px;
    font-weight: 600;
}

.dimension-drawing {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 30px;
    text-align: center;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* 文档内容 */
.documents-content h2 {
    color: #1f2937;
    font-size: 1.8em;
    margin-bottom: 25px;
    font-weight: 600;
}

.document-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.document-item {
    display: flex;
    align-items: center;
    gap: 20px;
    padding: 20px;
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.document-item:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

.doc-icon {
    font-size: 24px;
    flex-shrink: 0;
}

.doc-info {
    flex-grow: 1;
}

.doc-info h4 {
    color: #1f2937;
    font-size: 1.1em;
    margin-bottom: 5px;
    font-weight: 600;
}

.doc-info p {
    color: #6b7280;
    font-size: 14px;
}

.download-btn {
    background: #dc2626;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: background 0.3s ease;
}

.download-btn:hover {
    background: #b91c1c;
}

/* 底部信息 */
.footer {
    background: #f8fafc;
    border-top: 1px solid #e5e7eb;
    margin-top: 50px;
}

.footer-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 30px 20px;
}

.footer-content {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.company-info h3 {
    color: #1f2937;
    font-size: 1.2em;
    margin-bottom: 10px;
    font-weight: 600;
}

.company-info p {
    color: #6b7280;
    font-size: 14px;
    margin-bottom: 5px;
}

.footer-links {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
}

.footer-links a {
    color: #6b7280;
    text-decoration: none;
    font-size: 14px;
    transition: color 0.3s ease;
}

.footer-links a:hover {
    color: #dc2626;
}

/* 响应式设计 */
@media (max-width: 1024px) {
    .content-container {
        padding: 20px 15px;
    }

    .product-header {
        flex-direction: column;
        gap: 20px;
    }

    .controls-header {
        flex-direction: column;
        gap: 20px;
        text-align: center;
    }

    .chart-controls {
        justify-content: center;
    }

    .technical-tables {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .header-container {
        padding: 10px 15px;
    }

    .breadcrumb-container {
        padding: 10px 15px;
        font-size: 12px;
    }

    .content-container {
        padding: 15px 10px;
    }

    .product-title h1 {
        font-size: 1.8em;
    }

    .tab-navigation {
        overflow-x: auto;
        white-space: nowrap;
    }

    .tab-btn {
        padding: 12px 20px;
        font-size: 14px;
    }

    .controls-header {
        padding: 15px;
    }

    .controls-header h2 {
        font-size: 1.4em;
    }

    .chart-controls {
        flex-direction: column;
        gap: 15px;
        width: 100%;
    }

    .chart-wrapper {
        height: 400px;
        padding: 15px;
    }

    .chart-legend {
        flex-direction: column;
        gap: 15px;
        padding: 15px;
    }

    .legend-item {
        justify-content: center;
    }

    .operating-point {
        padding: 15px;
    }

    .technical-content h2,
    .dimensions-content h2,
    .documents-content h2 {
        font-size: 1.5em;
    }

    .document-item {
        flex-direction: column;
        text-align: center;
        gap: 15px;
    }

    .footer-content {
        text-align: center;
    }

    .footer-links {
        justify-content: center;
        gap: 15px;
    }
}

@media (max-width: 480px) {
    .product-title h1 {
        font-size: 1.5em;
    }

    .tab-btn {
        padding: 10px 15px;
        font-size: 13px;
    }

    .chart-wrapper {
        height: 350px;
        padding: 10px;
    }

    .controls-header h2 {
        font-size: 1.2em;
    }

    .chart-legend {
        gap: 10px;
    }

    .legend-item {
        font-size: 12px;
    }

    .footer-links {
        flex-direction: column;
        gap: 10px;
    }
}
