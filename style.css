/* 重置样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', 'Helvetica Neue', Helvetica, sans-serif;
    background-color: #f5f5f5;
    color: #333333;
    line-height: 1.4;
}

/* 顶部导航栏 */
.top-header {
    background: #003d6b;
    position: sticky;
    top: 0;
    z-index: 1000;
}

.header-container {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 20px;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 15px;
}

.menu-toggle {
    background: none;
    border: none;
    font-size: 18px;
    cursor: pointer;
    color: white;
}

.logo {
    height: 32px;
}

.header-right {
    display: flex;
    align-items: center;
}

.search-btn {
    background: none;
    border: none;
    font-size: 16px;
    cursor: pointer;
    color: white;
    padding: 8px;
}

/* 面包屑导航 */
.breadcrumb {
    background: #ffffff;
    border-bottom: 1px solid #ddd;
}

.breadcrumb-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 8px 20px;
    font-size: 13px;
}

.breadcrumb a {
    color: #0066cc;
    text-decoration: none;
}

.breadcrumb a:hover {
    text-decoration: underline;
}

.breadcrumb span {
    color: #666;
    margin: 0 6px;
}

.breadcrumb .current {
    color: #333;
    font-weight: normal;
}

/* 主要内容区域 */
.main-content {
    background: #ffffff;
}

.content-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* 产品标题区域 */
.product-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #ddd;
}

.product-title h1 {
    color: #003d6b;
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 5px;
}

.product-code {
    color: #666;
    font-size: 14px;
    font-weight: normal;
}

.product-image {
    flex-shrink: 0;
}

.pump-image-placeholder {
    background: #f8f8f8;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 15px;
}

/* 标签页导航 */
.tab-navigation {
    display: flex;
    border-bottom: 1px solid #ddd;
    margin-bottom: 20px;
    background: #f8f8f8;
}

.tab-btn {
    background: #f8f8f8;
    border: none;
    border-right: 1px solid #ddd;
    padding: 12px 20px;
    font-size: 14px;
    font-weight: normal;
    color: #333;
    cursor: pointer;
    transition: all 0.2s ease;
}

.tab-btn:hover {
    background: #e8e8e8;
}

.tab-btn.active {
    background: #ffffff;
    color: #003d6b;
    font-weight: bold;
    border-bottom: 2px solid #0066cc;
}

/* 标签页内容 */
.tab-content {
    min-height: 600px;
}

.tab-panel {
    display: none;
}

.tab-panel.active {
    display: block;
}

/* 概述内容 */
.overview-content h2 {
    color: #1f2937;
    font-size: 1.8em;
    margin-bottom: 20px;
}

.overview-content p {
    color: #4b5563;
    font-size: 1.1em;
    margin-bottom: 20px;
}

.key-features h3 {
    color: #1f2937;
    font-size: 1.4em;
    margin-bottom: 15px;
}

.key-features ul {
    list-style: none;
    padding-left: 0;
}

.key-features li {
    color: #4b5563;
    padding: 8px 0;
    padding-left: 25px;
    position: relative;
}

.key-features li:before {
    content: "✓";
    color: #dc2626;
    font-weight: bold;
    position: absolute;
    left: 0;
}

/* 曲线内容 */
.curves-content {
    background: #ffffff;
}

.controls-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding: 15px;
    background: #f0f0f0;
    border: 1px solid #ddd;
}

.controls-header h2 {
    color: #003d6b;
    font-size: 18px;
    font-weight: bold;
}

.chart-controls {
    display: flex;
    gap: 20px;
}

.control-group {
    display: flex;
    align-items: center;
    gap: 8px;
}

.control-group label {
    font-weight: normal;
    color: #333;
    font-size: 13px;
}

.control-group select {
    padding: 4px 8px;
    border: 1px solid #ccc;
    border-radius: 3px;
    background: white;
    font-size: 13px;
    color: #333;
    cursor: pointer;
}

.control-group select:focus {
    outline: none;
    border-color: #0066cc;
    box-shadow: 0 0 0 2px rgba(0, 102, 204, 0.2);
}

/* 图表区域 */
.chart-wrapper {
    position: relative;
    height: 450px;
    background: white;
    border: 1px solid #ddd;
    padding: 15px;
    margin-bottom: 15px;
}

.chart-legend {
    display: flex;
    justify-content: center;
    gap: 25px;
    flex-wrap: wrap;
    padding: 10px;
    background: #f8f8f8;
    border: 1px solid #ddd;
    margin-bottom: 15px;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 12px;
    font-weight: normal;
    color: #333;
}

.legend-color {
    width: 16px;
    height: 3px;
    border-radius: 1px;
}

.head-curve {
    background: #ff0000;
}

.efficiency-curve {
    background: #00aa00;
}

.power-curve {
    background: #0066cc;
}

.npsh-curve {
    background: #ff6600;
}

/* 工作点信息 */
.operating-point {
    background: #f0f8ff;
    padding: 15px;
    border: 1px solid #cce7ff;
    margin-top: 15px;
}

.operating-point h3 {
    color: #003d6b;
    font-size: 16px;
    margin-bottom: 10px;
    font-weight: bold;
}

#operatingPointInfo {
    color: #0066cc;
    font-size: 13px;
}

/* 技术数据内容 */
.technical-content h2 {
    color: #003d6b;
    font-size: 18px;
    margin-bottom: 20px;
    font-weight: bold;
}

.technical-tables {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.table-section h3 {
    color: #003d6b;
    font-size: 16px;
    margin-bottom: 10px;
    font-weight: bold;
    border-bottom: 2px solid #0066cc;
    padding-bottom: 5px;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    border: 1px solid #ddd;
}

.data-table tr {
    border-bottom: 1px solid #eee;
}

.data-table tr:last-child {
    border-bottom: none;
}

.data-table td {
    padding: 8px 12px;
    font-size: 13px;
}

.data-table td:first-child {
    background: #f8f8f8;
    font-weight: normal;
    color: #333;
    width: 50%;
}

.data-table td:last-child {
    color: #003d6b;
    font-weight: bold;
}

/* 尺寸内容 */
.dimensions-content h2 {
    color: #1f2937;
    font-size: 1.8em;
    margin-bottom: 25px;
    font-weight: 600;
}

.dimension-drawing {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 30px;
    text-align: center;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* 文档内容 */
.documents-content h2 {
    color: #1f2937;
    font-size: 1.8em;
    margin-bottom: 25px;
    font-weight: 600;
}

.document-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.document-item {
    display: flex;
    align-items: center;
    gap: 20px;
    padding: 20px;
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.document-item:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

.doc-icon {
    font-size: 24px;
    flex-shrink: 0;
}

.doc-info {
    flex-grow: 1;
}

.doc-info h4 {
    color: #1f2937;
    font-size: 1.1em;
    margin-bottom: 5px;
    font-weight: 600;
}

.doc-info p {
    color: #6b7280;
    font-size: 14px;
}

.download-btn {
    background: #dc2626;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: background 0.3s ease;
}

.download-btn:hover {
    background: #b91c1c;
}

/* 底部信息 */
.footer {
    background: #003d6b;
    color: white;
    margin-top: 30px;
}

.footer-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.footer-content {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.company-info h3 {
    color: white;
    font-size: 16px;
    margin-bottom: 8px;
    font-weight: bold;
}

.company-info p {
    color: #cce7ff;
    font-size: 13px;
    margin-bottom: 3px;
}

.footer-links {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.footer-links a {
    color: #cce7ff;
    text-decoration: none;
    font-size: 12px;
    transition: color 0.3s ease;
}

.footer-links a:hover {
    color: white;
}

/* 响应式设计 */
@media (max-width: 1024px) {
    .content-container {
        padding: 20px 15px;
    }

    .product-header {
        flex-direction: column;
        gap: 20px;
    }

    .controls-header {
        flex-direction: column;
        gap: 20px;
        text-align: center;
    }

    .chart-controls {
        justify-content: center;
    }

    .technical-tables {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .header-container {
        padding: 10px 15px;
    }

    .breadcrumb-container {
        padding: 10px 15px;
        font-size: 12px;
    }

    .content-container {
        padding: 15px 10px;
    }

    .product-title h1 {
        font-size: 1.8em;
    }

    .tab-navigation {
        overflow-x: auto;
        white-space: nowrap;
    }

    .tab-btn {
        padding: 12px 20px;
        font-size: 14px;
    }

    .controls-header {
        padding: 15px;
    }

    .controls-header h2 {
        font-size: 1.4em;
    }

    .chart-controls {
        flex-direction: column;
        gap: 15px;
        width: 100%;
    }

    .chart-wrapper {
        height: 400px;
        padding: 15px;
    }

    .chart-legend {
        flex-direction: column;
        gap: 15px;
        padding: 15px;
    }

    .legend-item {
        justify-content: center;
    }

    .operating-point {
        padding: 15px;
    }

    .technical-content h2,
    .dimensions-content h2,
    .documents-content h2 {
        font-size: 1.5em;
    }

    .document-item {
        flex-direction: column;
        text-align: center;
        gap: 15px;
    }

    .footer-content {
        text-align: center;
    }

    .footer-links {
        justify-content: center;
        gap: 15px;
    }
}

@media (max-width: 480px) {
    .product-title h1 {
        font-size: 1.5em;
    }

    .tab-btn {
        padding: 10px 15px;
        font-size: 13px;
    }

    .chart-wrapper {
        height: 350px;
        padding: 10px;
    }

    .controls-header h2 {
        font-size: 1.2em;
    }

    .chart-legend {
        gap: 10px;
    }

    .legend-item {
        font-size: 12px;
    }

    .footer-links {
        flex-direction: column;
        gap: 10px;
    }
}
