* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f5f7fa;
    color: #333;
    line-height: 1.6;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    background: white;
    box-shadow: 0 0 20px rgba(0,0,0,0.1);
    border-radius: 8px;
}

.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 0;
    border-bottom: 2px solid #e1e8ed;
    margin-bottom: 30px;
}

.product-info h1 {
    color: #1e3a8a;
    font-size: 2.2em;
    font-weight: 600;
    margin-bottom: 5px;
}

.product-code {
    color: #6b7280;
    font-size: 0.9em;
    margin-bottom: 5px;
}

.product-description {
    color: #374151;
    font-size: 1.1em;
}

.brand {
    font-size: 2em;
    font-weight: bold;
    color: #dc2626;
    letter-spacing: 2px;
}

.chart-container {
    margin-bottom: 40px;
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 15px;
    background: #f8fafc;
    border-radius: 6px;
}

.chart-header h2 {
    color: #1e3a8a;
    font-size: 1.5em;
}

.chart-controls {
    display: flex;
    gap: 20px;
}

.control-group {
    display: flex;
    align-items: center;
    gap: 8px;
}

.control-group label {
    font-weight: 500;
    color: #374151;
}

.control-group select {
    padding: 6px 12px;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    background: white;
    font-size: 0.9em;
}

.chart-wrapper {
    position: relative;
    height: 500px;
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    padding: 20px;
    margin-bottom: 20px;
}

.chart-legend {
    display: flex;
    justify-content: center;
    gap: 30px;
    flex-wrap: wrap;
    padding: 15px;
    background: #f8fafc;
    border-radius: 6px;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.9em;
    font-weight: 500;
}

.legend-color {
    width: 20px;
    height: 3px;
    border-radius: 2px;
}

.head-curve {
    background: #dc2626;
}

.efficiency-curve {
    background: #059669;
}

.power-curve {
    background: #2563eb;
}

.npsh-curve {
    background: #7c3aed;
}

.technical-data {
    margin-bottom: 30px;
}

.technical-data h3 {
    color: #1e3a8a;
    font-size: 1.3em;
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 2px solid #e1e8ed;
}

.data-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
}

.data-item {
    display: flex;
    justify-content: space-between;
    padding: 12px;
    background: #f8fafc;
    border-radius: 4px;
    border-left: 4px solid #dc2626;
}

.data-item label {
    font-weight: 500;
    color: #374151;
}

.data-item span {
    font-weight: 600;
    color: #1e3a8a;
}

.operating-point {
    background: #f0f9ff;
    padding: 20px;
    border-radius: 6px;
    border: 1px solid #bae6fd;
}

.operating-point h3 {
    color: #0c4a6e;
    margin-bottom: 10px;
}

#operatingPointInfo {
    color: #0369a1;
    font-size: 0.95em;
}

@media (max-width: 768px) {
    .container {
        padding: 15px;
        margin: 10px;
    }
    
    .header {
        flex-direction: column;
        text-align: center;
        gap: 15px;
    }
    
    .chart-header {
        flex-direction: column;
        gap: 15px;
    }
    
    .chart-controls {
        flex-direction: column;
        gap: 10px;
        width: 100%;
    }
    
    .chart-wrapper {
        height: 400px;
        padding: 10px;
    }
    
    .chart-legend {
        flex-direction: column;
        gap: 10px;
    }
    
    .data-grid {
        grid-template-columns: 1fr;
    }
}
