// 水泵性能曲线数据
const pumpData = {
    // 2900 rpm, 250mm 叶轮
    "2900_250": {
        // 流量 (m³/h)
        flow: [0, 50, 100, 150, 200, 250, 300, 350, 400, 450, 500],
        // 扬程 (m)
        head: [485, 480, 470, 455, 435, 410, 380, 345, 305, 260, 210],
        // 效率 (%)
        efficiency: [0, 45, 65, 75, 82, 85, 83, 78, 70, 58, 40],
        // 功率 (kW)
        power: [180, 190, 205, 220, 240, 260, 280, 295, 305, 310, 300],
        // NPSH (m)
        npsh: [8, 8.2, 8.5, 9, 9.8, 10.8, 12, 13.5, 15.2, 17, 19]
    },
    
    // 2900 rpm, 240mm 叶轮
    "2900_240": {
        flow: [0, 50, 100, 150, 200, 250, 300, 350, 400, 450, 480],
        head: [440, 435, 425, 410, 390, 365, 335, 300, 260, 215, 180],
        efficiency: [0, 42, 62, 72, 80, 83, 81, 76, 68, 56, 45],
        power: [165, 175, 190, 205, 225, 245, 265, 280, 290, 295, 285],
        npsh: [7.5, 7.7, 8, 8.5, 9.2, 10.2, 11.5, 13, 14.7, 16.5, 18]
    },
    
    // 2900 rpm, 230mm 叶轮
    "2900_230": {
        flow: [0, 50, 100, 150, 200, 250, 300, 350, 400, 430],
        head: [395, 390, 380, 365, 345, 320, 290, 255, 215, 180],
        efficiency: [0, 40, 60, 70, 78, 81, 79, 74, 66, 58],
        power: [150, 160, 175, 190, 210, 230, 250, 265, 275, 270],
        npsh: [7, 7.2, 7.5, 8, 8.7, 9.7, 11, 12.5, 14.2, 15.5]
    },
    
    // 1450 rpm, 250mm 叶轮
    "1450_250": {
        flow: [0, 25, 50, 75, 100, 125, 150, 175, 200, 225, 250],
        head: [121, 120, 117, 114, 109, 102, 95, 86, 76, 65, 52],
        efficiency: [0, 45, 65, 75, 82, 85, 83, 78, 70, 58, 40],
        power: [22, 24, 26, 28, 30, 32, 35, 37, 38, 39, 37],
        npsh: [2, 2.1, 2.1, 2.3, 2.4, 2.7, 3, 3.4, 3.8, 4.3, 4.8]
    },
    
    // 1450 rpm, 240mm 叶轮
    "1450_240": {
        flow: [0, 25, 50, 75, 100, 125, 150, 175, 200, 225, 240],
        head: [110, 109, 106, 102, 97, 91, 84, 75, 65, 54, 45],
        efficiency: [0, 42, 62, 72, 80, 83, 81, 76, 68, 56, 45],
        power: [21, 22, 24, 26, 28, 31, 33, 35, 36, 37, 36],
        npsh: [1.9, 1.9, 2, 2.1, 2.3, 2.6, 2.9, 3.3, 3.7, 4.1, 4.5]
    },
    
    // 1450 rpm, 230mm 叶轮
    "1450_230": {
        flow: [0, 25, 50, 75, 100, 125, 150, 175, 200, 215],
        head: [99, 98, 95, 91, 86, 80, 73, 64, 54, 45],
        efficiency: [0, 40, 60, 70, 78, 81, 79, 74, 66, 58],
        power: [19, 20, 22, 24, 26, 29, 31, 33, 34, 34],
        npsh: [1.8, 1.8, 1.9, 2, 2.2, 2.4, 2.8, 3.1, 3.6, 3.9]
    }
};

// 获取指定转速和叶轮直径的数据
function getPumpData(speed, impeller) {
    const key = `${speed}_${impeller}`;
    return pumpData[key] || pumpData["2900_250"];
}

// 根据流量插值计算其他参数
function interpolateValue(flow, flowArray, valueArray) {
    if (flow <= flowArray[0]) return valueArray[0];
    if (flow >= flowArray[flowArray.length - 1]) return valueArray[valueArray.length - 1];
    
    for (let i = 0; i < flowArray.length - 1; i++) {
        if (flow >= flowArray[i] && flow <= flowArray[i + 1]) {
            const ratio = (flow - flowArray[i]) / (flowArray[i + 1] - flowArray[i]);
            return valueArray[i] + ratio * (valueArray[i + 1] - valueArray[i]);
        }
    }
    return 0;
}

// 获取工作点信息
function getOperatingPoint(flow, speed, impeller) {
    const data = getPumpData(speed, impeller);
    
    return {
        flow: flow,
        head: interpolateValue(flow, data.flow, data.head),
        efficiency: interpolateValue(flow, data.flow, data.efficiency),
        power: interpolateValue(flow, data.flow, data.power),
        npsh: interpolateValue(flow, data.flow, data.npsh)
    };
}
